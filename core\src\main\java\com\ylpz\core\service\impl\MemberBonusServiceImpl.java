package com.ylpz.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.constants.BrokerageRecordConstants;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.constants.MemberParamConstants;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.BonusRecordRequest;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.dao.UserBrokerageRecordDao;
import com.ylpz.core.dao.UserDao;
import com.ylpz.core.service.MemberBonusService;
import com.ylpz.core.service.MemberParamConfigService;
import com.ylpz.core.service.UserService;
import com.ylpz.model.system.MemberParamConfig;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserBrokerageRecord;
import com.ylpz.model.user.UserSalesRank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 奖励金服务实现类
 */
@Service
public class MemberBonusServiceImpl implements MemberBonusService {

    @Resource
    private UserBrokerageRecordDao userBrokerageRecordDao;

    @Resource
    private UserDao userDao;

    @Autowired
    private MemberParamConfigService memberParamConfigService;

    @Autowired
    private UserService userService;

    /**
     * 获取奖励金记录列表
     *
     * @param request          查询条件
     * @param pageParamRequest 分页参数
     * @return 奖励金记录分页数据
     */
    @Override
    public PageInfo<UserBrokerageRecord> getBonusList(BonusRecordRequest request, PageParamRequest pageParamRequest) {
        // 构建查询条件
        LambdaQueryWrapper<UserBrokerageRecord> lqw = new LambdaQueryWrapper<>();

        // 筛选奖励金相关记录（根据标题或者来源类型）
        lqw.and(wrapper -> {
            // 根据奖励类型筛选
            if (!StringUtils.isEmpty(request.getBonusType())) {
                // 不同的奖励类型对应不同的标题或者来源
                if ("upgrade".equals(request.getBonusType())) {
                    wrapper.like(UserBrokerageRecord::getTitle, "升级为VIP");
                } else if ("firstOrder".equals(request.getBonusType())) {
                    wrapper.like(UserBrokerageRecord::getTitle, "首单购买");
                } else if ("recharge".equals(request.getBonusType())) {
                    wrapper.like(UserBrokerageRecord::getTitle, "会员充值");
                } else if ("rank".equals(request.getBonusType())) {
                    wrapper.like(UserBrokerageRecord::getTitle, "排行榜");

                    // 如果指定了排行榜类型，则进一步筛选
                    if (!StringUtils.isEmpty(request.getRankType())) {
                        if ("week".equals(request.getRankType())) {
                            wrapper.like(UserBrokerageRecord::getTitle, "周排行榜");
                        } else if ("month".equals(request.getRankType())) {
                            wrapper.like(UserBrokerageRecord::getTitle, "月排行榜");
                        } else if ("quarter".equals(request.getRankType())) {
                            wrapper.like(UserBrokerageRecord::getTitle, "季排行榜");
                        }
                    }
                }
            }
        });

        // 用户筛选
        if (request.getUid() != null) {
            lqw.eq(UserBrokerageRecord::getUid, request.getUid());
        }

        // 通过手机号查询用户ID
        if (!StringUtils.isEmpty(request.getMobile())) {
            LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
            userQuery.eq(User::getPhone, request.getMobile());
            List<User> users = userDao.selectList(userQuery);
            if (!CollectionUtils.isEmpty(users)) {
                List<Integer> userIds = users.stream().map(User::getId).collect(Collectors.toList());
                lqw.in(UserBrokerageRecord::getUid, userIds);
            } else {
                // 如果没有找到用户，返回空结果
                return new PageInfo<>();
            }
        }

        // 关联订单筛选
        if (!StringUtils.isEmpty(request.getLinkId())) {
            lqw.eq(UserBrokerageRecord::getLinkId, request.getLinkId());
        }

        // 状态筛选
        if (request.getStatus() != null) {
            lqw.eq(UserBrokerageRecord::getStatus, request.getStatus());
        } else {
            // 默认查询已完成的奖励记录
            lqw.eq(UserBrokerageRecord::getStatus, BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
        }

        // 时间范围筛选
        if (request.getStartTime() != null && request.getEndTime() != null) {
            lqw.between(UserBrokerageRecord::getCreateTime, request.getStartTime(), request.getEndTime());
        }

        // 只查询增加类型的记录
        lqw.eq(UserBrokerageRecord::getType, BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);

        // 按创建时间倒序排序
        lqw.orderByDesc(UserBrokerageRecord::getCreateTime);

        // 执行分页查询
        Page<UserBrokerageRecord> page = new Page<>(pageParamRequest.getPage(), pageParamRequest.getLimit());
        Page<UserBrokerageRecord> pageResult = userBrokerageRecordDao.selectPage(page, lqw);

        // 查询用户信息并填充
        List<UserBrokerageRecord> records = pageResult.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            fillUserInfo(records);
        }

        // 计算符合条件的奖励金总额
        BigDecimal totalAmount = records.stream()
                .map(UserBrokerageRecord::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 使用CommonPage工具类创建分页信息
        PageInfo<UserBrokerageRecord> pageInfo = new PageInfo<>(records);
        pageInfo.setPages((int) pageResult.getPages());
        pageInfo.setPageNum((int) pageResult.getCurrent());
        pageInfo.setPageSize((int) pageResult.getSize());
        pageInfo.setTotal(pageResult.getTotal());
        return pageInfo;
    }

    /**
     * 填充用户信息
     *
     * @param records 佣金记录列表
     */
    private void fillUserInfo(List<UserBrokerageRecord> records) {
        List<Integer> userIds = records.stream()
                .map(UserBrokerageRecord::getUid)
                .distinct()
                .collect(Collectors.toList());

        LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
        userQuery.in(User::getId, userIds);
        List<User> users = userDao.selectList(userQuery);

        for (UserBrokerageRecord record : records) {
            for (User user : users) {
                if (record.getUid().equals(user.getId())) {
                    record.setUserName(user.getNickname());
                    break;
                }
            }
        }
    }

    /**
     * 发放排行榜奖励金
     *
     * @param rankType 排行榜类型（周排行、月排行、季度排行）
     * @param rankDate 排行日期
     * @return 是否发放成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributeRankBonus(String rankType, Date rankDate) {
        // 1. 查询对应排行榜奖励配置
        String sourceType = "";
        if ("week".equals(rankType)) {
            sourceType = MemberParamConstants.BONUS_SOURCE_WEEKLY_RANK;
        } else if ("month".equals(rankType)) {
            sourceType = MemberParamConstants.BONUS_SOURCE_MONTHLY_RANK;
        } else if ("quarter".equals(rankType)) {
            sourceType = MemberParamConstants.BONUS_SOURCE_QUARTERLY_RANK;
        } else {
            return false;
        }

        // 2. 获取排行榜配置
        List<MemberParamConfig> configs = memberParamConfigService.getListBySourceType(sourceType);
        if (CollectionUtils.isEmpty(configs)) {
            return false;
        }

        // 3. 获取销售排行数据
        List<UserSalesRank> salesRanks = getSalesRankList(rankType, rankDate);
        if (CollectionUtils.isEmpty(salesRanks)) {
            return false;
        }

        // 4. 发放奖励
        for (MemberParamConfig config : configs) {
            // 查找备注中的排名信息，如"周排行榜第一名奖励500元"
            String remark = config.getRemark();
            Integer rank = extractRankFromRemark(remark);
            if (rank == null || rank > salesRanks.size()) {
                continue;
            }

            // 获取对应排名的用户
            UserSalesRank userRank = salesRanks.get(rank - 1);

            // 创建并保存奖励记录
            UserBrokerageRecord record = new UserBrokerageRecord();
            record.setUid(userRank.getUid());
            record.setLinkId(rankType + "_" + DateUtil.dateToStr(rankDate, Constants.DATE_TIME_FORMAT_NUM));
            record.setLinkType("rank");
            record.setType(BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
            record.setTitle(sourceType);
            record.setPrice(new BigDecimal(config.getNumber()));
            record.setBalance(getUserBrokerage(userRank.getUid()).add(record.getPrice()));
            record.setMark("获得" + sourceType + "第" + rank + "名，奖励" + config.getNumber() + config.getUnit());
            record.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());

            // 保存记录并更新用户佣金余额
            userBrokerageRecordDao.insert(record);
            // 使用适当的方法更新用户余额
            updateUserBrokerage(userRank.getUid(), record.getPrice());
        }

        return true;
    }

    /**
     * 发放推广升级奖励金
     *
     * @param uid       被推广用户ID
     * @param spreadUid 推广人用户ID
     * @return 是否发放成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributeUpgradeBonus(Integer uid, Integer spreadUid) {
        if (uid == null || spreadUid == null) {
            return false;
        }

        // 查询升级奖励配置
        LambdaQueryWrapper<MemberParamConfig> configQuery = new LambdaQueryWrapper<>();
        configQuery.eq(MemberParamConfig::getConfigType, MemberParamConstants.CONFIG_TYPE_BONUS);
        configQuery.eq(MemberParamConfig::getSourceType, MemberParamConstants.BONUS_SOURCE_UPGRADE);
        configQuery.eq(MemberParamConfig::getStatus, true);
        List<MemberParamConfig> configs = memberParamConfigService.list(configQuery);

        if (CollectionUtils.isEmpty(configs)) {
            return false;
        }

        MemberParamConfig config = configs.get(0);

        // 获取被推广用户和推广人信息
        User promotedUser = userService.getById(uid);
        User promoter = userService.getById(spreadUid);

        if (promotedUser == null || promoter == null) {
            return false;
        }

        // 创建奖励记录
        UserBrokerageRecord record = new UserBrokerageRecord();
        record.setUid(spreadUid);
        record.setLinkId(uid.toString());
        record.setLinkType("upgrade");
        record.setType(BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
        record.setTitle(MemberParamConstants.BONUS_SOURCE_UPGRADE);
        record.setPrice(new BigDecimal(config.getNumber()));
        record.setBalance(getUserBrokerage(spreadUid).add(record.getPrice()));
        record.setMark("推广用户 " + promotedUser.getNickname() + " 升级为VIP，获得奖励" + config.getNumber() + config.getUnit());
        record.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());

        // 保存记录并更新用户佣金余额
        userBrokerageRecordDao.insert(record);
        // 使用适当的方法更新用户余额
        updateUserBrokerage(spreadUid, record.getPrice());

        return true;
    }

    /**
     * 发放推广充值奖励金
     *
     * @param uid            充值用户ID
     * @param spreadUid      推广人用户ID
     * @param rechargeAmount 充值金额
     * @return 是否发放成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributeRechargeBonus(Integer uid, Integer spreadUid, BigDecimal rechargeAmount) {
        if (uid == null || spreadUid == null || rechargeAmount == null
                || rechargeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 查询充值奖励配置
        LambdaQueryWrapper<MemberParamConfig> configQuery = new LambdaQueryWrapper<>();
        configQuery.eq(MemberParamConfig::getConfigType, MemberParamConstants.CONFIG_TYPE_BONUS);
        configQuery.eq(MemberParamConfig::getSourceType, MemberParamConstants.BONUS_SOURCE_RECHARGE);
        configQuery.eq(MemberParamConfig::getStatus, true);
        List<MemberParamConfig> configs = memberParamConfigService.list(configQuery);

        if (CollectionUtils.isEmpty(configs)) {
            return false;
        }

        MemberParamConfig config = configs.get(0);

        // 获取充值用户和推广人信息
        User rechargeUser = userService.getById(uid);
        User promoter = userService.getById(spreadUid);

        if (rechargeUser == null || promoter == null) {
            return false;
        }

        // 计算奖励金额
        BigDecimal bonusAmount;
        if ("%".equals(config.getUnit())) {
            // 按比例计算
            bonusAmount = rechargeAmount.multiply(new BigDecimal(config.getRatio())).divide(new BigDecimal(100), 2,
                    BigDecimal.ROUND_HALF_UP);
        } else {
            // 固定金额
            bonusAmount = new BigDecimal(config.getNumber());
        }

        // 创建奖励记录
        UserBrokerageRecord record = new UserBrokerageRecord();
        record.setUid(spreadUid);
        record.setLinkId(uid.toString());
        record.setLinkType("recharge");
        record.setType(BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
        record.setTitle(MemberParamConstants.BONUS_SOURCE_RECHARGE);
        record.setPrice(bonusAmount);
        record.setBalance(getUserBrokerage(spreadUid).add(record.getPrice()));
        record.setMark("推广用户 " + rechargeUser.getNickname() + " 充值" + rechargeAmount + "元，获得奖励" + bonusAmount + "元");
        record.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());

        // 保存记录并更新用户佣金余额
        userBrokerageRecordDao.insert(record);
        // 使用适当的方法更新用户余额
        updateUserBrokerage(spreadUid, record.getPrice());

        return true;
    }

    /**
     * 发放首单购买奖励金
     *
     * @param uid         购买用户ID
     * @param spreadUid   推广人用户ID
     * @param orderNo     订单号
     * @param orderAmount 订单金额
     * @return 是否发放成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributeFirstOrderBonus(Integer uid, Integer spreadUid, String orderNo, BigDecimal orderAmount) {
        if (uid == null || spreadUid == null || StringUtils.isEmpty(orderNo) || orderAmount == null
                || orderAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 查询首单奖励配置
        LambdaQueryWrapper<MemberParamConfig> configQuery = new LambdaQueryWrapper<>();
        configQuery.eq(MemberParamConfig::getConfigType, MemberParamConstants.CONFIG_TYPE_BONUS);
        configQuery.eq(MemberParamConfig::getSourceType, MemberParamConstants.BONUS_SOURCE_FIRST_ORDER);
        configQuery.eq(MemberParamConfig::getStatus, true);
        List<MemberParamConfig> configs = memberParamConfigService.list(configQuery);

        if (CollectionUtils.isEmpty(configs)) {
            return false;
        }

        MemberParamConfig config = configs.get(0);

        // 获取购买用户和推广人信息
        User buyUser = userService.getById(uid);
        User promoter = userService.getById(spreadUid);

        if (buyUser == null || promoter == null) {
            return false;
        }

        // 检查是否是首单
        LambdaQueryWrapper<UserBrokerageRecord> recordQuery = new LambdaQueryWrapper<>();
        recordQuery.eq(UserBrokerageRecord::getLinkType, "firstOrder");
        recordQuery.eq(UserBrokerageRecord::getUid, spreadUid);
        recordQuery.eq(UserBrokerageRecord::getLinkId, uid.toString());
        long count = userBrokerageRecordDao.selectCount(recordQuery);
        if (count > 0) {
            // 已经发放过首单奖励
            return false;
        }

        // 计算奖励金额
        BigDecimal bonusAmount;
        if ("%".equals(config.getUnit())) {
            // 按比例计算
            bonusAmount = orderAmount.multiply(new BigDecimal(config.getRatio())).divide(new BigDecimal(100), 2,
                    BigDecimal.ROUND_HALF_UP);
        } else {
            // 固定金额
            bonusAmount = new BigDecimal(config.getNumber());
        }

        // 创建奖励记录
        UserBrokerageRecord record = new UserBrokerageRecord();
        record.setUid(spreadUid);
        record.setLinkId(uid.toString());
        record.setLinkType("firstOrder");
        record.setType(BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
        record.setTitle(MemberParamConstants.BONUS_SOURCE_FIRST_ORDER);
        record.setPrice(bonusAmount);
        record.setBalance(getUserBrokerage(spreadUid).add(record.getPrice()));
        record.setMark("推广用户 " + buyUser.getNickname() + " 首单购买，订单号：" + orderNo + "，获得奖励" + bonusAmount + "元");
        record.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());

        // 保存记录并更新用户佣金余额
        userBrokerageRecordDao.insert(record);
        // 使用适当的方法更新用户余额
        updateUserBrokerage(spreadUid, record.getPrice());

        return true;
    }

    /**
     * 获取奖励金合计金额
     *
     * @param request 查询条件
     * @return 奖励金合计金额
     */
    @Override
    public BigDecimal getBonusTotal(BonusRecordRequest request) {
        // 构建查询条件
        LambdaQueryWrapper<UserBrokerageRecord> lqw = buildBonusListQueryWrapper(request);

        // 只查询增加类型的记录
        lqw.eq(UserBrokerageRecord::getType, BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);

        // 只查询已完成状态的记录
        lqw.eq(UserBrokerageRecord::getStatus, BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);

        // 查询记录列表
        List<UserBrokerageRecord> records = userBrokerageRecordDao.selectList(lqw);

        // 计算合计金额
        return records.stream()
                .map(UserBrokerageRecord::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 构建奖励金列表查询条件
     *
     * @param request 查询条件
     * @return 查询条件
     */
    private LambdaQueryWrapper<UserBrokerageRecord> buildBonusListQueryWrapper(BonusRecordRequest request) {
        LambdaQueryWrapper<UserBrokerageRecord> lqw = new LambdaQueryWrapper<>();

        // 筛选奖励金相关记录（根据标题或者来源类型）
        lqw.and(wrapper -> {
            // 根据奖励类型筛选
            if (!StringUtils.isEmpty(request.getBonusType())) {
                // 不同的奖励类型对应不同的标题或者来源
                if ("upgrade".equals(request.getBonusType())) {
                    wrapper.like(UserBrokerageRecord::getTitle, "升级为VIP");
                } else if ("firstOrder".equals(request.getBonusType())) {
                    wrapper.like(UserBrokerageRecord::getTitle, "首单购买");
                } else if ("recharge".equals(request.getBonusType())) {
                    wrapper.like(UserBrokerageRecord::getTitle, "会员充值");
                } else if ("rank".equals(request.getBonusType())) {
                    wrapper.like(UserBrokerageRecord::getTitle, "排行榜");

                    // 如果指定了排行榜类型，则进一步筛选
                    if (!StringUtils.isEmpty(request.getRankType())) {
                        if ("week".equals(request.getRankType())) {
                            wrapper.like(UserBrokerageRecord::getTitle, "周排行榜");
                        } else if ("month".equals(request.getRankType())) {
                            wrapper.like(UserBrokerageRecord::getTitle, "月排行榜");
                        } else if ("quarter".equals(request.getRankType())) {
                            wrapper.like(UserBrokerageRecord::getTitle, "季排行榜");
                        }
                    }
                }
            }
        });

        // 用户筛选
        if (request.getUid() != null) {
            lqw.eq(UserBrokerageRecord::getUid, request.getUid());
        }

        // 通过手机号查询用户ID
        if (!StringUtils.isEmpty(request.getMobile())) {
            LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
            userQuery.eq(User::getPhone, request.getMobile());
            List<User> users = userDao.selectList(userQuery);
            if (!CollectionUtils.isEmpty(users)) {
                List<Integer> userIds = users.stream().map(User::getId).collect(Collectors.toList());
                lqw.in(UserBrokerageRecord::getUid, userIds);
            }
        }

        // 关联订单筛选
        if (!StringUtils.isEmpty(request.getLinkId())) {
            lqw.eq(UserBrokerageRecord::getLinkId, request.getLinkId());
        }

        // 状态筛选
        if (request.getStatus() != null) {
            lqw.eq(UserBrokerageRecord::getStatus, request.getStatus());
        }

        // 时间范围筛选
        if (request.getStartTime() != null && request.getEndTime() != null) {
            lqw.between(UserBrokerageRecord::getCreateTime, request.getStartTime(), request.getEndTime());
        }

        return lqw;
    }

    /**
     * 从备注中提取排名信息
     *
     * @param remark 备注信息
     * @return 排名序号
     */
    private Integer extractRankFromRemark(String remark) {
        if (StringUtils.isEmpty(remark)) {
            return null;
        }

        // 使用正则表达式提取排名信息
        Pattern pattern = Pattern.compile("第(\\d+)名");
        Matcher matcher = pattern.matcher(remark);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }

        return null;
    }

    /**
     * 获取用户佣金余额
     *
     * @param uid 用户ID
     * @return 佣金余额
     */
    private BigDecimal getUserBrokerage(Integer uid) {
        User user = userDao.selectById(uid);
        if (user != null) {
            return user.getBrokeragePrice();
        }
        return BigDecimal.ZERO;
    }

    /**
     * 更新用户佣金余额
     *
     * @param uid   用户ID
     * @param price 增加的金额
     */
    private void updateUserBrokerage(Integer uid, BigDecimal price) {
        User user = userDao.selectById(uid);
        if (user != null) {
            // 更新用户佣金余额
            user.setBrokeragePrice(user.getBrokeragePrice().add(price));
            userDao.updateById(user);
        }
    }

    /**
     * 获取销售排行榜数据
     *
     * @param rankType 排行榜类型
     * @param rankDate 排行日期
     * @return 销售排行数据
     */
    private List<UserSalesRank> getSalesRankList(String rankType, Date rankDate) {
        // 模拟实现，实际应从相关服务获取
        // TODO: 实现从销售排行服务获取数据
        return null;
    }
}