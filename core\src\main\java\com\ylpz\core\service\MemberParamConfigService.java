package com.ylpz.core.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.model.system.MemberParamConfig;

/**
 * MemberParamConfigService 接口
 */
public interface MemberParamConfigService extends IService<MemberParamConfig> {

    /**
     * 根据配置类型获取会员参数设置列表
     * 
     * @param configType 配置类型
     * @return 会员参数设置列表
     */
    List<MemberParamConfig> getListByConfigType(Integer configType);

    /**
     * 获取所有会员参数设置列表
     * 
     * @return 会员参数设置列表
     */
    List<MemberParamConfig> getList();

    /**
     * 获取所有启用状态的会员参数设置列表
     * 
     * @return 启用状态的会员参数设置列表
     */
    List<MemberParamConfig> getEnabledList();

    /**
     * 按配置类型批量保存或更新会员参数设置
     * 
     * @param configType  配置类型
     * @param configList, 配置列表
     * @return 是否成功
     */
    Boolean saveOrUpdateBatchByType(Integer configType, List<MemberParamConfig> configList);

    /**
     * 根据来源类型获取奖励金配置列表
     * 
     * @param sourceType 奖励金来源类型
     * @return 奖励金配置列表
     */
    List<MemberParamConfig> getListBySourceType(String sourceType);
}