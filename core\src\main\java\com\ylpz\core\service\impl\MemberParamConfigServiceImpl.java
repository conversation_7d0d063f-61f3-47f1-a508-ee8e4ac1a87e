package com.ylpz.core.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.common.constants.MemberParamConstants;
import com.ylpz.core.common.request.MemberParamConfigRequest;
import com.ylpz.core.dao.MemberParamConfigDao;
import com.ylpz.core.service.MemberParamConfigService;
import com.ylpz.model.system.MemberParamConfig;

import cn.hutool.core.util.StrUtil;

/**
 * MemberParamConfigServiceImpl
 */
@Service
public class MemberParamConfigServiceImpl extends ServiceImpl<MemberParamConfigDao, MemberParamConfig>
        implements MemberParamConfigService {

    @Resource
    private MemberParamConfigDao dao;

    /**
     * 根据配置类型获取会员参数设置列表
     * 
     * @param configType 配置类型
     * @return 会员参数设置列表
     */
    @Override
    public List<MemberParamConfig> getListByConfigType(Integer configType) {
        LambdaQueryWrapper<MemberParamConfig> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MemberParamConfig::getConfigType, configType);
        lqw.orderByAsc(MemberParamConfig::getSort);
        return list(lqw);
    }

    /**
     * 获取所有会员参数设置列表
     * 
     * @return 会员参数设置列表
     */
    @Override
    public List<MemberParamConfig> getList() {
        LambdaQueryWrapper<MemberParamConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(MemberParamConfig::getConfigType).orderByAsc(MemberParamConfig::getSort);
        return list(queryWrapper);
    }

    /**
     * 获取所有启用状态的会员参数设置列表
     * 
     * @return 启用状态的会员参数设置列表
     */
    @Override
    public List<MemberParamConfig> getEnabledList() {
        LambdaQueryWrapper<MemberParamConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberParamConfig::getStatus, true).orderByAsc(MemberParamConfig::getConfigType)
                .orderByAsc(MemberParamConfig::getSort);
        return list(queryWrapper);
    }

    /**
     * 校验来源类型或提现类型的有效性
     */
    private void checkSourceTypeValid(MemberParamConfigRequest request) {
        Integer configType = request.getConfigType();

        if (MemberParamConstants.CONFIG_TYPE_GROWTH.equals(configType)) {
            // 成长值设置
            if (StrUtil.isBlank(request.getSourceType())) {
                throw new RuntimeException("成长值来源类型不能为空");
            }

            boolean isValid = Arrays.asList(MemberParamConstants.EXPERIENCE_SOURCE_TYPES)
                    .contains(request.getSourceType());
            if (!isValid) {
                throw new RuntimeException("无效的成长值来源类型");
            }
        } else if (MemberParamConstants.CONFIG_TYPE_WITHDRAW.equals(configType)) {
            // 提现设置
            if (StrUtil.isBlank(request.getWithdrawLevels())) {
                throw new RuntimeException("提现等级限制不能为空");
            }
        } else if (MemberParamConstants.CONFIG_TYPE_BONUS.equals(configType)) {
            // 奖励金设置
            if (StrUtil.isBlank(request.getSourceType())) {
                throw new RuntimeException("奖励金来源类型不能为空");
            }

            boolean isValid = Arrays.asList(MemberParamConstants.BONUS_SOURCE_TYPES).contains(request.getSourceType());
            if (!isValid) {
                throw new RuntimeException("无效的奖励金来源类型");
            }
        } else if (MemberParamConstants.CONFIG_TYPE_COMMISSION.equals(configType)) {
            // 佣金返现设置
            if (StrUtil.isBlank(request.getWithdrawType())) {
                throw new RuntimeException("会员类型不能为空");
            }

            boolean isValid = Arrays.asList(MemberParamConstants.WITHDRAW_TYPES).contains(request.getWithdrawType());
            if (!isValid) {
                throw new RuntimeException("无效的会员类型");
            }
        }
    }

    /**
     * 检查配置类型相关参数是否合法
     */
    private void checkConfigTypeValid(MemberParamConfigRequest request) {
        Integer configType = request.getConfigType();

        // 检查成长值设置参数
        if (configType.equals(MemberParamConstants.CONFIG_TYPE_GROWTH)) {
            if (StringUtils.isEmpty(request.getSourceType())) {
                throw new RuntimeException("成长值来源类型不能为空");
            }
            if (request.getNumber() == null) {
                throw new RuntimeException("每消费数量不能为空");
            }
            if (StringUtils.isEmpty(request.getUnit())) {
                throw new RuntimeException("单位不能为空");
            }
            if (request.getRatio() == null) {
                throw new RuntimeException("获取比例不能为空");
            }
        }
        // 检查提现设置参数
        else if (configType.equals(MemberParamConstants.CONFIG_TYPE_WITHDRAW)) {
            if (StringUtils.isEmpty(request.getWithdrawLevels())) {
                throw new RuntimeException("提现等级限制不能为空");
            }
            if (request.getMinAmount() == null) {
                throw new RuntimeException("提现最小金额不能为空");
            }
            if (request.getMaxAmount() == null) {
                throw new RuntimeException("提现最大金额不能为空");
            }
            if (request.getFeeRate() == null) {
                throw new RuntimeException("提现手续费不能为空");
            }
        }
        // 检查奖励金设置参数
        else if (configType.equals(MemberParamConstants.CONFIG_TYPE_BONUS)) {
            if (StringUtils.isEmpty(request.getSourceType())) {
                throw new RuntimeException("奖励金来源类型不能为空");
            }
            if (request.getNumber() == null) {
                throw new RuntimeException("奖励数量不能为空");
            }
            if (StringUtils.isEmpty(request.getUnit())) {
                throw new RuntimeException("单位不能为空");
            }
            // 如果是排行榜相关奖励，检查排名展示数量
            if (request.getSourceType().contains("排行榜")) {
                if (request.getRankDisplayCount() == null) {
                    throw new RuntimeException("榜单排名显示数量不能为空");
                }
                if (request.getRankDisplayCount() < 1) {
                    throw new RuntimeException("榜单排名显示数量不能小于1");
                }
            }
        }
        // 检查佣金返现设置参数
        else if (configType.equals(MemberParamConstants.CONFIG_TYPE_COMMISSION)) {
            if (StringUtils.isEmpty(request.getWithdrawType())) {
                throw new RuntimeException("会员类型不能为空");
            }
            if (request.getRatio() == null) {
                throw new RuntimeException("佣金返现比例不能为空");
            }
            if (request.getAutoRefund() == null) {
                request.setAutoRefund(false);
            }
        } else {
            throw new RuntimeException("不支持的配置类型");
        }
    }

    /**
     * 检查相同类型的配置是否已存在
     */
    private boolean checkTypeExists(MemberParamConfigRequest request) {
        LambdaQueryWrapper<MemberParamConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberParamConfig::getConfigType, request.getConfigType());

        // 不同配置类型的查询条件
        if (request.getConfigType().equals(MemberParamConstants.CONFIG_TYPE_GROWTH)) {
            // 成长值设置，根据来源类型查询
            wrapper.eq(MemberParamConfig::getSourceType, request.getSourceType());
        } else if (request.getConfigType().equals(MemberParamConstants.CONFIG_TYPE_WITHDRAW)) {
            // 提现设置，根据提现等级限制查询
            wrapper.eq(MemberParamConfig::getWithdrawLevels, request.getWithdrawLevels());
        } else if (request.getConfigType().equals(MemberParamConstants.CONFIG_TYPE_BONUS)) {
            // 奖励金设置，根据来源类型查询
            wrapper.eq(MemberParamConfig::getSourceType, request.getSourceType());
        } else if (request.getConfigType().equals(MemberParamConstants.CONFIG_TYPE_COMMISSION)) {
            // 佣金返现设置，根据会员类型查询
            wrapper.eq(MemberParamConfig::getWithdrawType, request.getWithdrawType());
        }

        // 排除自身
        if (request.getId() != null) {
            wrapper.ne(MemberParamConfig::getId, request.getId());
        }

        Long count = baseMapper.selectCount(wrapper);
        return count > 0;
    }

    /**
     * 按配置类型批量保存或更新会员参数设置
     * 
     * @param configType 配置类型
     * @param configList 配置列表
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateBatchByType(Integer configType, List<MemberParamConfig> configList) {
        if (CollectionUtils.isEmpty(configList)) {
            return true; // 空列表直接返回成功
        }

        // 获取数据库中该类型的所有配置
        List<MemberParamConfig> existingConfigs = getListByConfigType(configType);

        // 提取现有配置的ID列表
        List<Integer> existingIds = existingConfigs.stream().map(MemberParamConfig::getId).collect(Collectors.toList());

        // 提取新配置中的ID列表
        List<Integer> newIds = configList.stream().map(MemberParamConfig::getId).filter(id -> id != null)
                .collect(Collectors.toList());

        // 设置当前时间
        Date now = new Date();

        // 更新时间和类型
        for (MemberParamConfig config : configList) {
            config.setConfigType(configType);

            if (config.getId() == null) {
                // 新增的配置
                config.setCreateTime(now);
                config.setUpdateTime(now);

                // 设置默认值
                if (config.getStatus() == null) {
                    config.setStatus(true);
                }
                if (config.getSort() == null) {
                    config.setSort(0);
                }
            } else {
                // 更新的配置
                config.setUpdateTime(now);
            }
        }

        // 批量保存或更新
        boolean success = saveOrUpdateBatch(configList);

        return success;
    }

    /**
     * 根据来源类型获取奖励金配置列表
     * 
     * @param sourceType 奖励金来源类型
     * @return 奖励金配置列表
     */
    @Override
    public List<MemberParamConfig> getListBySourceType(String sourceType) {
        LambdaQueryWrapper<MemberParamConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberParamConfig::getConfigType, MemberParamConstants.CONFIG_TYPE_BONUS);
        queryWrapper.eq(MemberParamConfig::getSourceType, sourceType);
        queryWrapper.eq(MemberParamConfig::getStatus, true);
        queryWrapper.orderByAsc(MemberParamConfig::getSort);
        return list(queryWrapper);
    }
}